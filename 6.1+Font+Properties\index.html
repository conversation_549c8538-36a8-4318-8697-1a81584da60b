<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>CSS Properties</title>
  <style>
    body {
      background-color: cornflowerblue;
      color: white;
      font-size: 18px;
    }

    /* Don't change the CSS above, add Your CSS below */
    #color{
      color: coral;
    }
    #size{
      font-size: 2rem;
    }
    #weight{
      font-weight: 900;
    }
    #family{
      font-family: "Caveat", cursive;
    }
    #align{
      text-align: right;
    }
    html{
      font-size: 30px;
    }

    
  </style>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400..700&display=swap" rel="stylesheet">
</head>

<body>
  <h1>Important CSS Properties</h1>
  <p id="color">Color</p>
  <p id="size">Font Size</p>
  <p id="weight">Font Weight</p>
  <p id="family">Font Family</p>
  <p id="align">Text Align</p>

  <!-- TODOs
  1. Change the color of <p>Color</p> to "coral" color.
  2. Change the font size of <p>Font Size</p> to 2X the size of the root font size.
  3. Change the font weight of <p>Font Weight</p> to 900.
  4. Change the font family of <p>Font Family</p> to the Google font Caveat with regular (400) font weight.
  Link: https://fonts.google.com/specimen/Caveat
  5. Change the <p>Text Align</p> to right align.
  6. Change the the root (html element) font size to 30px -->
</body>

</html>