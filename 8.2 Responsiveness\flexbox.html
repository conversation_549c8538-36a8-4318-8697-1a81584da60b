<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSS Flexbox</title>
  <style>
    .flex-container {
      display: flex;
    }

    .card {
      background: blue;
      border: 30px solid white;
      height: 100px;
      flex: 1;
    }

    .first {
      flex: 2;
    }

    .second {
      flex: 0.5;
    }
  </style>
</head>

<body>
  <h1>CSS Flexbox</h1>
  <div class="flex-container">
    <div class="first card"></div>
    <div class="second card"></div>
    <div class="card"></div>
    <div class="card"></div>
  </div>


</body>

</html>