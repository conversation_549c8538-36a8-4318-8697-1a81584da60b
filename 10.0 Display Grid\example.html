<!DOCTYPE html>
<html>

<head>
  <title>CSS Grid vs Flexbox</title>
  <style>
    .container {
      display: grid;
      grid-template-columns: 1fr 2fr;
      grid-template-rows: 1fr 1fr;
      gap: 10px
    }

    .container>* {
      background-color: cadetblue;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="one">
      <p>Lorem ipsum dolor, sit amet consectetur adipisicing elit. Ut error ea neque! Minima fuga deserunt delectus
        corrupti molestiae, eos esse quos vel reiciendis neque quibusdam!</p>
    </div>
    <div class="two">
      <p>Lorem ipsum dolor sit, amet consectetur adipisicing elit. Corporis eius, consequuntur facere iure error fugiat
        architecto illo, repudiandae magni doloremque non molestiae qui expedita iste!</p>
    </div>
    <div class="three">
      <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Repudiandae porro a harum in voluptate reprehenderit
        necessitatibus, placeat cumque est odio accusamus! Quaerat nesciunt tempora dolorum voluptate ex dolor natus
        fuga possimus totam ipsam, saepe odio nemo officia accusamus nam voluptates magnam soluta autem animi quia
        veniam praesentium excepturi? Incidunt, quia.</p>
    </div>
    <div>
      <p>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Esse, expedita ex culpa corporis quidem amet ducimus
        aut facilis harum atque veniam similique facere quia odio exercitationem, aliquam voluptates at molestiae!</p>
    </div>
  </div>

</body>

</html>