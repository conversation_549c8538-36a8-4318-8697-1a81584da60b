<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fixed Size Grid</title>
  <style>

    .grid-container {
      display: grid;
      grid-template-rows: 100px 200px;
      grid-template-columns: 400px 800px;
    }

    .grid-item {
      font-size: 3rem;
      background-color: darkviolet;
      border: 5px solid goldenrod;
      color: white;
    }
  </style>
</head>

<body>
  <h1>Fixed Size Grid</h1>
  <div class="grid-container">
    <div class="grid-item">1</div>
    <div class="grid-item">2</div>
    <div class="grid-item">3</div>
    <div class="grid-item">4</div>
  </div>
</body>

</html>