/*document.getElementsByTagName("li");
document.getElementsByClassName("btn");
HTMLCollection [button.btn]
document.getElementsByClassName("btn").style.color="pink";
VM72:1 Uncaught TypeError: Cannot set properties of undefined (setting 'color')
    at <anonymous>:1:51
(anonymous) @ VM72:1Understand this error
document.getElementsByClassName("btn")[0].style.color="pink";
'pink'
document.getElementById("title");
<h1 id=​"title">​Hello​</h1>​
document.getElementById("title").innerHTML = "GoodBye";
'GoodBye'




document.querySelector("title");
<title>​My Website​</title>​
document.querySelector("h1");
<h1 id=​"title">​Hello​</h1>​
document.querySelector("#title");
<h1 id=​"title">​Hello​</h1>​
document.querySelector(".btn");
<button class=​"btn">​Click Me​</button>​
document.querySelector("li a");
<a href=​"https:​/​/​www.google.com">​Google​</a>​
document.querySelector("item.li");
null
document.querySelector("li.list");
<li class=​"list">​…​</li>​
document.querySelector("#item a");


document.querySelector("#item .list");
<li class=​"list">​…​</li>​
document.querySelectorAll("#item .list");


//challenge
document.querySelector("li a").style.color = "red";
'red'


//challenge
document.querySelector(".btn").style.backgroundColor="yellow";




//good practice
document.querySelector("h1").classList.add("huge");//reference from css
document.querySelector("h1").classList.remove("huge");
document.querySelector("h1").classList.toggle("huge");//acts like switch



//DIFF BTWN INNERHTML VS TEXTCONTENT
document.querySelector("h1").innerHTML;
'<strong>Hello</strong>'
document.querySelector("h1").textContent;
'Hello'




//ATTRIBUTE PROPERTY 
document.querySelector("a");
<a href=​"https:​/​/​www.google.com">​Google​</a>​
document.querySelector("a").attributes;
NamedNodeMap {0: href, href: href, length: 1}
document.querySelector("a").getAttribute("href");
'https://www.google.com'
document.querySelector("a").setAttribute("href","hhtps://www.bing.com");
undefined

*/