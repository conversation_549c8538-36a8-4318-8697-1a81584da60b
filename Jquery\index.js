//$("h1").css("color", "red");

//$("h1")selecting used instead of both document.queryselector and document.querySelectorAll.



//$("h1").addClass("big-title"); //add 1 class


//$("h1").addClass("big-title margin-50") //adding 2 classes
//$("h1").hasClass("big-title margin-50");//returns true or false

//$("h1").text("Hamba");//changing text
//$("h1").html("<em>Hamba</em>");

//$("h1").text("<em>Hamba</em>");

//$("a").attr("href","https://www.yahoo.com");

/*$("h1").click(function(){
    $("h1").css("color","purple");
});*/

//without jQuery
/*for (var i = 0; i < document.querySelectorAll("button").length; i++) {
  document.querySelectorAll("button")[i].addEventListener("click", function () {
    document.querySelector("h1").style.color = "purple";
  });
}*/


//withjQuery

/*$("button").click(function(){
    $("h1").css("color","purple");
})*/



/*$(document).keydown(function(event){
    $("h1").text(event.key);
    $("h1").css("color", "purple");
    console.log(event.key)
})*/


//$("h1").before("<button>New</button>");
//$("h1").after("<button>New</button>");
//$("h1").prepend("<button>New</button>");
$("h1").append("<button>New</button>");
//$("button").remove();

$("button").on("click", function () {
  $("h1").toggle("slow");
});



