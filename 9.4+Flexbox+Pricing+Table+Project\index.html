<!DOCTYPE html>
<html>
  <head>
    <title>Flexbox Pricing Table</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Sono:wght@400;700&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Sono", sans-serif;
      }
      .pricing-container{
        display: flex ;
        gap: 30px;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
      .pricing-plan{
        flex: 1;
        max-width: 400PX;
        background-color: rgba(217, 213, 213, 0.437);
        text-align: center;
        padding: 20px;
        border-radius: 0.3rem ;
      }
      .plan-button{
        background-color: rgb(255, 105, 6);
        border-style: none ;
        border-radius: 0.2rem;
        color: white;
        width: 70px;
        height: 30px;
      }
      .plan-features{
        list-style: none;
        padding: 1px;
        margin: 0;
        
      }
      .plan-title {
        font-size: 1.5rem;
        font-weight: 800;
        margin-bottom: 10px;

      }
      .plan-price{
        font-size: 3rem;
        font-weight: 800;
        margin: 0;

      }
      li{
        padding: 10px 0px;
      }


      /* Hint: What can you do with a media query and flexbox? */
      @media (max-width: 1250px) {
        .pricing-container{
        flex-direction: column;
        height: 100%;
      }

      }
    </style>
  </head>

  <body>
    <div class="pricing-container">
      <div class="pricing-plan">
        <div class="plan-title">Basic</div>
        <div class="plan-price">$11.99/month</div>
        <ul class="plan-features">
          <li>✅ 10GB Storage</li>
          <li>✅ 1 User</li>
          <li>🚫 Support</li>
        </ul>
        <button class="plan-button">Sign Up</button>
      </div>
      <div class="pricing-plan">
        <div class="plan-title">Standard</div>
        <div class="plan-price">$19.99/month</div>
        <ul class="plan-features">
          <li>✅ 50GB Storage</li>
          <li>✅ 5 Users</li>
          <li>✅ Phone &amp; Email Support</li>
        </ul>
        <button class="plan-button">Sign Up</button>
      </div>
      <div class="pricing-plan">
        <div class="plan-title">Premium</div>
        <div class="plan-price">$49.99/month</div>
        <ul class="plan-features">
          <li>✅ 100GB Storage</li>
          <li>✅ 10 Users</li>
          <li>✅ 24/7 Support</li>
        </ul>
        <button class="plan-button">Sign Up</button>
      </div>
    </div>
  </body>
</html>
