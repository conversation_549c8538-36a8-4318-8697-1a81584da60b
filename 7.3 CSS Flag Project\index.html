<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>CSS Flag Project</title>
  <style>
    /* Write your CSS Code here */
.flag{
      background-color: #ce1127 ;
      width: 900px;
      height: 600px;
      position: relative;
}
p{
  font-size: 5rem;
  padding: 0;
  margin: 0;
  color: white;
  text-align: center;
}
.flag > div{
  background-color: #002768;
  position: absolute;
  width: 100%;
  height: 300px;
  top: 150px;
}
.flag > div > div{
  background-color: white;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  position: absolute;
  left: 350px;
  top: 50px;
}
.flag>div div p {
      color: black;
    }

    
  </style>
</head>

<!-- 
  IMPORTANT! Do not change any HTML
Don't add any classes/ids/elements 
Use what you know about combining selectors 
and CSS specificity instead.
Hint 1: The flag is 900px by 600px and the circle is 200px by 200px.
Hint 2: You can use CSS inspection to get the colors from
https://appbrewery.github.io/flag-of-laos/
-->

<body>
  <div class="flag">
    <p>The Flag</p>
    <div>
      <div>
        <p>of Laos</p>
      </div>
    </div>
  </div>
</body>

</html>