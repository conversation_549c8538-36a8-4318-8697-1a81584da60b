body{
    margin: 0;
    padding: 0;
}

#title{
    background-color:#e4f9f5;
    padding-top: 50px;
    padding-left: 50px;
    padding-right: 50px;
    padding-bottom: 0 ;
    position: relative;
}
.top-cloud{
    position: absolute;    
    right: 300px;
    top: 40px;
}
.title-text{
    text-align: center;
}
h1{
    color: #66bfbf;
    font-size: 5.625rem;
    margin: 50px auto 0 auto;
    font-family: "Sacramento", cursive;
    text-align: center;
}
h2 {
    color: #66bfbf;
    font-family: "Montserrat", sans-serif;
    font-size: 2.5rem;
    font-weight: normal;
    padding-bottom: 10px;
}
.bottom-cloud{
    position: absolute;
    left: 300px;
    bottom: 250px;
}
.mountain{
    padding-left: 400px;
    
   
    
}
#features{
    text-align: center;
    padding: 50px;
  
    
}
#skills{
    text-align: center;
    padding: 50px;

}
.container{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}


#contact{
    text-align: center;
    font-size: 2rem;
    margin: 5px;
}
button{
    width: 100px;
    height: 40px;
    padding: 2px;
    border-radius: 5px;
}

#footer{
    background-color: #66bfbf;
    margin-bottom: 0;
    padding: 20px;

}
.footer , p{
    margin: 0;
    padding: 30px;
    text-align: center;
}
.links{
    display: flex;
    list-style: none;
    justify-content: center;
    gap: 10px;
}
.links > li > a{
    color: white;
    text-decoration: none;
} 