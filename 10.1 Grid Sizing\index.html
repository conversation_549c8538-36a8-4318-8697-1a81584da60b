<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Grid Fundamentals</title>
  <style>
    body {
      font-family: sans-serif;
    }

    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: 100vh;
      background-color: #f2f2f2;
    }

    .item {
      width: 200px;
      margin: 20px;
      background-color: #3498db;
      color: #fff;
      text-align: center;
      line-height: 2;
      font-size: 24px;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>Grid Sizing</h1>
    <a class="item" href="./fixed-size.html">Fixed Size</a>
    <a class="item" href="./auto-size.html">Auto Size</a>
    <a class="item" href="./fractional-size.html">Fractional Size</a>
    <a class="item" href="./minmax-size.html">MinMax Size</a>
    <a class="item" href="./repeat.html">Repeat</a>
    <a class="item" href="./test.html">Test</a>


  </div>
</body>

</html>