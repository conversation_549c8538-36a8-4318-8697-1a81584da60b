<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Bootstrap</title>
  <!-- TODO 1: Add the Bootstrap link here. -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
  <style>
    /* TODO 4: Use flexbox to center the card in the vertical and horizontal center. */
    .flex-container{
      display: flex;
      height: 100vh;
      justify-content: center;
      align-items: center;
    }

  </style>
</head>

<body>
  <div class="flex-container">
    <!-- TODO 2: Add the Bootstrap Prebuilt Card here -->
    <!-- TODO 3: Change the image src to display the flower.jpg image. -->
    <div class="card" style="width: 18rem;">
      <img src="flower.jpg" class="card-img-top" alt="sun-flower">
      <div class="card-body">
        <h5 class="card-title">Card title</h5>
        <p class="card-text">Some quick example text to build on the card title and make up the bulk of the card’s
          content.</p>
        <a href="#" class="btn btn-primary">Go somewhere</a>
      </div>
    </div>
  </div>
</body>

</html>