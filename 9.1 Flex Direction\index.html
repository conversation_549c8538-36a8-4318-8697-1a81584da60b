<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flex Direction</title>
  <!-- HINT:
  https://developer.mozilla.org/en-US/docs/Web/CSS/Universal_selectors
  https://developer.mozilla.org/en-US/docs/Learn/CSS/Building_blocks/Selectors/Combinators 
  -->
  <style>
    .container {
      color: white;
      border: 5px solid gold;
      display: inline-flex;
      flex-direction: column;
    }
    .container > * {
      flex-basis: 100px;
    }

    /*
Select all the elements that are the direct
children of the .container class.
    */

    .red {background-color: #eb4d4b;}
    .orange {background-color: #f0932b;}
    .yellow {background-color: #f6e58d;}
    .green {background-color: #6ab04c;}
    .blue {background-color: #4834d4;}
    .indigo {background-color: #30336b;}
    .purple {background-color: #be2edd;}
  </style>
</head>

<body>
  <div class="container">
    <div class="red">Red </div>
    <div class="orange">Orange</div>
    <div class="yellow">Yellow</div>
    <div class="green">Green</div>
    <div class="blue">Blue</div>
    <div class="indigo">Indigo</div>
    <div class="purple">Purple</div>
  </div>
  
</body>

</html>