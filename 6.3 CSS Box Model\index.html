<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>CSS Box Model</title>
  <style>
    /* Write your CSS code here */
    div{
      width: 200px;
      height: 200px;
    }
    #container1{
      background-color: cadetblue;
      border: 10px solid black;
      padding: 20px;
    }
    #container2{
      margin-left: 260px;
      background-color: gold;
      border:solid black;
      border-width: 20px 10px;
    }
    #container3{
     background-color: indianred;
     border: 10px solid black;
     margin-left: 40px;
    }
    p{
      margin: 0;
    }

  </style>
</head>

<body>
  <div id="container1">
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam at sapien porttitor urna elementum lacinia. In
    id magna pulvinar, ultricies lorem id, vehicula elit. Aliquam eu luctus nisl, vitae pellentesque magna. Phasellus
    dolor metus, laoreet ac convallis sit amet, efficitur sed dolor.</p>
  </div>
  <div id="container2">

  </div>
  <div id="container3">

  </div>


  <!-- TODOs:
1. Create 3 Boxes using the div element.
2. Set their sizes to 200px heigh by 200px wide.
3. Set different background colors for each of the boxes (I used cadetblue, gold and indianred).
4. Add a paragraph <p> element into the first div and add the following words:
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam at sapien porttitor urna elementum lacinia. In
    id magna pulvinar, ultricies lorem id, vehicula elit. Aliquam eu luctus nisl, vitae pellentesque magna. Phasellus
    dolor metus, laoreet ac convallis sit amet, efficitur sed dolor.
5. Set the 1st div to have 20px padding all around with a black 10px border.
6. Fix the style of the <p> element to remove all margins.
Hint: Use the CSS inspector in Chrome.
7. Set the 2nd div to have a 20px border on top and bottom and 10px border left and right. (See goal image)
8. Set the 3rd div to have a 10px border 
9. Set the margins for the divs so that each box corner touches the other. (See the goal image)
-->

</body>

</html>